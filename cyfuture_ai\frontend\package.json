{"name": "agro", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@react-oauth/google": "^0.12.2", "@tailwindcss/vite": "^4.1.8", "auth": "^1.2.3", "axios": "^1.9.0", "chart.js": "^4.4.9", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "next-auth": "^4.24.11", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.1", "tailwindcss": "^4.1.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}