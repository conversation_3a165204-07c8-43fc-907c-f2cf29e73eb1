require("dotenv").config();
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('./src/models/User');

const createTestUser = async () => {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/cyfuture_ai';
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    // Check if test user already exists
    const existingUser = await User.findOne({ email: '<EMAIL>' });
    if (existingUser) {
      console.log('Test user already exists:');
      console.log('Email: <EMAIL>');
      console.log('Password: password123');
      return;
    }

    // Create test user
    const hashedPassword = await bcrypt.hash('password123', 10);
    const testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: hashedPassword
    });

    console.log('Test user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: password123');
    console.log('User ID:', testUser._id);

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
};

createTestUser();
